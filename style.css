* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: sans-serif;
}
nav {
  width: 100%;
  background-color: #f97316;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 60px;
}
.nav-links {
  display: flex;
  gap: 30px;
  list-style: none;
}
.nav-links ul {
  display: flex;
  gap: 50px;
  align-items: center;
}
.nav-links li {
  list-style: none;
}
.nav-links li a {
  font-size: 18px;
  text-decoration: none;
  color: black;
}
nav .logo {
  width: 180px;
  /* background-color: red; */
}
nav .logo img {
  width: 100%;
}
.actions-button {
  display: flex;
  align-items: center;
  gap: 20px;
}
.actions-button button {
  padding: 10px 27px;
  border-radius: 30px;
  border: none;
  font-size: 15px;
}

.hero-section {
  width: 100%;
  padding: 100px 0px 200px 0px;
  background-color: #f97316;
}
.hero-contents {
  width: 70%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 0 auto;
}
.hero-contents p {
  margin-bottom: 30px;
}
.hero-contents p {
  font-size: 26px;
}
.hero-contents h1 {
  font-size: 4.5rem;
}
.hero-contents a {
  margin-top: 30px;
  text-decoration: none;
  border-radius: 30px;
  padding: 16px 35px;
  /* border: 1px solid black; */
  background-color: black;
  color: white;
  font-size: 18px;
  font-weight: 500;
}
.hero-contents h1:nth-child(2) {
  color: white;
}
.hero-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image-wrapper .hero-image img {
  width: 70%;
  margin-top: -110px;
  border: 12px solid #f97316;
  border-top-left-radius: 150px;
  border-bottom-right-radius: 150px;
}
.work-section {
  width: 100%;
  padding: 100px 30px;
}
.work-section-contents {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.work-section-contents h2 {
  font-size: 42px;
}
/* cards  */
.cards-wrapper {
  display: flex;
  justify-content: space-between;
  text-align: center;
  padding: 0px 40px;
  width: 100%;
  margin-top: 75px;
}
.cards-wrapper .card {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.cards-wrapper .card p {
  width: 300px;
  font-size: 18px;
}

.thumbnail-img1 {
  width: 80%;
}
.thumbnail-img1 img {
  width: 100%;
}

.thumbnail-img2 {
  width: 95%;
}
.thumbnail-img2 img {
  width: 100%;
}
.thumbnail-img3 {
  width: 78%;
}
.thumbnail-img3 img {
  width: 100%;
}

.card-content {
  margin-top: 35px;
}
.card-content h2{
  font-size: 31px;
  font-weight: 400;
}

.card-content p {
  margin-top: 26px;
}
.aboutus-section{
  width: 100%;
  padding: 100px 0px;
 background: linear-gradient(180deg, rgba(224, 91, 0, 1) 0%, rgba(0, 0, 0, 1) 100%);
}
.flex-box{
  width: 90%;
  /* background-color: yellow; */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.left{
  max-width: 50%;
}
.left h2{
  font-size: 42px;
  color: white;
}
.left p{
  font-size: 20px;
  margin-top: 20px;
  color: white;
}
.left h2:nth-child(2) {
  margin-top: 10px;

}
.right{
  max-width: 50%;
}
.right img{

  width: 355px;
}

.testimonial-section {
      text-align: center;
      padding: 60px 20px;
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 40px;
      font-family: 'Orbitron', sans-serif;
    }

    .swiper {
       padding-bottom: 50px;      /*make space for bullets */
    }

    .swiper-slide {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      max-width: 320px;
      margin: auto;
      box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .swiper-slide img {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      margin-bottom: 20px;
    }

    .swiper-slide h3 {
      font-size: 1.2rem;
      margin-bottom: 10px;
      color: #111;
    }

    .swiper-slide p {
      font-size: 1rem;
      color: #444;
      line-height: 1.5;
    }

    .swiper-pagination-bullet {
      background-color: #ccc;
      opacity: 1;
    }

    .swiper-pagination-bullet-active {
      background-color: orange;
    }